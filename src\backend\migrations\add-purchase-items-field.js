const { sequelize } = require('../src/config/database');

/**
 * 添加items字段到purchase表的迁移脚本
 */
async function addItemsField() {
  const queryInterface = sequelize.getQueryInterface();

  try {
    console.log('开始添加purchase表的缺失字段...');

    // 直接尝试添加字段，如果已存在会抛出错误但不影响其他字段
    const fieldsToAdd = [
      {
        name: 'items',
        definition: {
          type: sequelize.Sequelize.JSON,
          allowNull: true,
          defaultValue: []
        }
      },
      {
        name: 'expectedDeliveryDate',
        definition: {
          type: sequelize.Sequelize.DATE,
          allowNull: true
        }
      },
      {
        name: 'deliveryAddress',
        definition: {
          type: sequelize.Sequelize.TEXT,
          allowNull: true
        }
      },
      {
        name: 'recipient',
        definition: {
          type: sequelize.Sequelize.STRING,
          allowNull: true
        }
      }
    ];

    for (const field of fieldsToAdd) {
      try {
        console.log(`添加${field.name}字段到purchase表...`);
        await queryInterface.addColumn('purchase', field.name, field.definition);
        console.log(`成功添加${field.name}字段`);
      } catch (error) {
        if (error.message.includes('already exists') || error.message.includes('duplicate column')) {
          console.log(`${field.name}字段已存在，跳过`);
        } else {
          console.error(`添加${field.name}字段失败:`, error.message);
          // 继续处理其他字段，不抛出错误
        }
      }
    }

  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  }
}

// 如果直接运行这个脚本
if (require.main === module) {
  addItemsField()
    .then(() => {
      console.log('迁移完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('迁移失败:', error);
      process.exit(1);
    });
}

module.exports = { addItemsField };
