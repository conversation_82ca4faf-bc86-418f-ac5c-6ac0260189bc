const { sequelize } = require('../src/config/database');

/**
 * 添加items字段到purchases表的迁移脚本
 */
async function addItemsField() {
  const queryInterface = sequelize.getQueryInterface();
  
  try {
    // 检查字段是否已存在
    const tableDescription = await queryInterface.describeTable('purchases');
    
    if (!tableDescription.items) {
      console.log('添加items字段到purchases表...');
      
      await queryInterface.addColumn('purchases', 'items', {
        type: sequelize.Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      });
      
      console.log('成功添加items字段');
    } else {
      console.log('items字段已存在，跳过');
    }
    
    // 检查expectedDeliveryDate字段是否已存在
    if (!tableDescription.expectedDeliveryDate) {
      console.log('添加expectedDeliveryDate字段到purchases表...');
      
      await queryInterface.addColumn('purchases', 'expectedDeliveryDate', {
        type: sequelize.Sequelize.DATE,
        allowNull: true
      });
      
      console.log('成功添加expectedDeliveryDate字段');
    } else {
      console.log('expectedDeliveryDate字段已存在，跳过');
    }
    
    // 检查deliveryAddress字段是否已存在
    if (!tableDescription.deliveryAddress) {
      console.log('添加deliveryAddress字段到purchases表...');
      
      await queryInterface.addColumn('purchases', 'deliveryAddress', {
        type: sequelize.Sequelize.TEXT,
        allowNull: true
      });
      
      console.log('成功添加deliveryAddress字段');
    } else {
      console.log('deliveryAddress字段已存在，跳过');
    }
    
    // 检查recipient字段是否已存在
    if (!tableDescription.recipient) {
      console.log('添加recipient字段到purchases表...');
      
      await queryInterface.addColumn('purchases', 'recipient', {
        type: sequelize.Sequelize.STRING,
        allowNull: true
      });
      
      console.log('成功添加recipient字段');
    } else {
      console.log('recipient字段已存在，跳过');
    }
    
  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  }
}

// 如果直接运行这个脚本
if (require.main === module) {
  addItemsField()
    .then(() => {
      console.log('迁移完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('迁移失败:', error);
      process.exit(1);
    });
}

module.exports = { addItemsField };
